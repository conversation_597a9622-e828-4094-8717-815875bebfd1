{"models": {"main": {"provider": "gemini", "baseURL": "http://***********/gemini/v1beta", "modelId": "gemini-2.5-pro", "maxTokens": 1048576, "temperature": 0.2}, "research": {"provider": "gemini", "baseURL": "http://***********/gemini/v1beta", "modelId": "gemini-2.5-pro", "maxTokens": 1048576, "temperature": 0.1}, "fallback": {"provider": "gemini", "baseURL": "http://***********/gemini/v1beta", "modelId": "gemini-2.5-pro", "maxTokens": 1048576, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 10, "defaultSubtasks": 5, "defaultPriority": "medium", "defaultTag": "master", "projectName": "futu-mcp-server", "ollamaBaseURL": "http://localhost:11434/api", "azureBaseURL": "https://your-endpoint.azure.com/openai/deployments", "vertexProjectId": "your-gcp-project-id", "vertexLocation": "us-central1", "responseLanguage": "Chinese", "userId": "7519436820318985"}}