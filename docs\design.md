
# 技术设计文档 (design.md) - v1.0 FINAL

## 1. 项目目标
本项目旨在基于`fastmcp`框架，将`py-futu-api`全面工具化，构建一个可通过AI智能体（Agent）驱动的股票分析与（模拟）交易平台。平台需具备高可靠性、易用性和可扩展性，支持从简单行情查询到复杂策略监控的多种应用场景。

## 2. 技术选型与理由
- **核心框架: `fastmcp`**
  - **理由**: 该框架轻量、专注，其核心的`Controller`（我们称之为Agent）和`Tool`模型与本项目需求（将API封装为工具供Agent调用）完美匹配。它提供了统一的范式来定义工具和构建智能体，避免了引入更重、更复杂的通用型Agent框架，简化了架构。

- **编程语言: `Python 3.11+`**
  - **理由**: `py-futu-api` 和 `fastmcp` 均基于Python。同时，Python拥有强大的数据科学生态（Pandas, NumPy），便于未来进行更深入的数据分析。

- **Futu API库: `py-futu-api`**
  - **理由**: 官方推荐的Python SDK，与FutuOpenD的兼容性和功能覆盖最全面。

- **数据模型与验证: `Pydantic V2`**
  - **理由**: 强制性的数据校验可以极大地提升代码的健壮性。Pydantic与`fastmcp`无缝集成，是定义工具输入输出模型的最佳选择，能确保传入Futu API的数据格式永远正确。

- **配置管理: `python-dotenv`**
  - **理由**: 根据需求确认，使用`.env`文件进行配置。`python-dotenv`库是加载这类文件的标准实践，简单且高效。

- **依赖管理: `uv`**
  - **理由**: `uv`库是一个用 Rust 写的超快 Python 包和项目管理工具。

## 3. 总体架构
系统是事件驱动的，由用户或预设逻辑触发AI智能体（Agent）。Agent解析任务，选择一个或多个Futu工具来执行，并返回结果。

**核心流程**: `用户指令 -> FutuAgent (Controller) -> 工具箱 (Toolbox) -> Futu API 客户端 -> FutuOpenD`

```mermaid
graph TD
    subgraph 用户层
        A[用户/脚本]
    end

    subgraph 应用层 (Python Application)
        B[FutuAgent / Controller]
        C[Toolbox: MCP 工具集]
        D[Futu API Client / Wrapper]
        E[配置模块 (.env)]
        F[日志模块]
    end

    subgraph 外部依赖
        G[FutuOpenD 网关]
    end

    A -- "执行'查询腾讯控股股价'" --> B
    B -- "分析任务, 选择工具" --> C
    C -- "调用 get_market_snapshot 工具" --> D
    D -- "建立连接, 发送/接收报文" --> G
    D -- "连接信息" --> E
    B -- "记录决策" --> F
    D -- "记录API交互" --> F
    G -- "返回股价数据" --> D
    D -- "格式化为Pydantic模型" --> C
    C -- "返回工具结果" --> B
    B -- "生成最终答案" --> A
```

## 4. 项目目录结构
```
fastmcp-futu-agent/
├── .env                  # 存储敏感配置信息
├── .gitignore
├── pyproject.toml        # 项目依赖与元数据 (使用Poetry或PDM管理)
├── README.md
└── src/
    ├── __init__.py
    ├── agents/           # 存放所有智能体 (fastmcp.Controller)
    │   ├── __init__.py
    │   ├── base_agent.py # 基础智能体，定义通用行为
    │   └── examples/     # 存放示例智能体
    │       ├── __init__.py
    │       ├── market_scanner.py
    │       └── price_alerter.py
    ├── config.py         # 加载并提供配置的模块
    ├── core/             # 存放核心的、被多处共享的逻辑
    │   ├── __init__.py
    │   └── futu_client.py  # 封装Futu API连接与生命周期管理
    ├── tools/            # 存放所有Futu API工具
    │   ├── __init__.py
    │   ├── base_tool.py    # 所有工具的基类
    │   ├── market_tools.py # 行情工具
    │   └── trade_tools.py  # 交易工具
    ├── models/           # 存放所有Pydantic数据模型
    │   ├── __init__.py
    │   ├── market_models.py
    │   └── trade_models.py
    ├── main.py             # 项目入口，用于启动某个Agent
    └── utils/            # 通用工具函数 (如日志配置)
        ├── __init__.py
        └── logging_setup.py
```

## 5. 数据表结构
本项目核心功能不涉及持久化数据库，所有股票数据均通过API实时获取。日志将根据配置输出到控制台或日志文件，不涉及数据库表结构。

## 6. 核心接口设计 (API)
此处的"API"指代我们提供给`fastmcp` Agent调用的内部工具接口，而非对外暴露的HTTP API。每个工具都是一个函数，其输入输出都由Pydantic模型严格定义。

**示例: 获取股票快照工具 (`market_tools.py`)**
```python
# models/market_models.py
from pydantic import BaseModel, Field
from typing import List

class StockSnapshotInput(BaseModel):
    stock_codes: List[str] = Field(..., description="股票代码列表，例如 ['HK.00700', 'US.AAPL']")

class StockSnapshotData(BaseModel):
    stock_code: str
    last_price: float
    volume: int
    # ... 其他需要的快照字段

class StockSnapshotOutput(BaseModel):
    snapshots: List[StockSnapshotData]

# tools/market_tools.py
# @mcp.tool(...)
# def get_market_snapshot(params: StockSnapshotInput) -> StockSnapshotOutput:
#     # 1. 调用 core.futu_client 获取数据
#     # 2. 将返回的原始数据转换为 StockSnapshotOutput 模型
#     # 3. 返回 Pydantic 对象
#     ...
```

## 7. 错误处理机制
1.  **Futu API 错误**: `core/futu_client.py` 中将捕获所有 `py-futu-api` 可能抛出的异常。这些异常将被包装成一个自定义的`FutuApiException`，并清晰地向上层（工具层）传递错误信息。
2.  **工具层错误**: 每个工具内部都会有`try...except`块，捕获`FutuApiException`。捕获后，工具不会让程序崩溃，而是会返回一个结构化的错误信息给Agent，例如 `{ "success": false, "error": "FUTU: 1001 - Ticker not found" }`。
3.  **Agent决策错误**: Agent在选择工具或解析参数失败时，`fastmcp`框架本身会进行处理。我们需确保日志能记录下这些失败，便于调试。